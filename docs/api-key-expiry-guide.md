# API Key 过期时间管理指南

## 概述

Claude Relay Service 支持为 API Keys 设置过期时间，提供了灵活的过期管理功能，方便进行权限控制和安全管理。

## 功能特性

- ✅ 创建时设置过期时间
- ✅ 随时修改过期时间
- ✅ 自动禁用过期的 Keys
- ✅ 手动续期功能
- ✅ 批量续期支持
- ✅ Web 界面和 CLI 双重管理

## CLI 管理工具

### 1. 查看 API Keys

```bash
npm run cli keys
# 选择 "📋 查看所有 API Keys"
```

显示内容包括：
- 名称和部分 Key
- 活跃/禁用状态
- 过期时间（带颜色提示）
- Token 使用量
- Token 限制

### 2. 修改过期时间

```bash
npm run cli keys
# 选择 "🔧 修改 API Key 过期时间"
```

支持的过期选项：
- ⏰ **1小时后**（测试用）
- 📅 **1天后**
- 📅 **7天后**
- 📅 **30天后**
- 📅 **90天后**
- 📅 **365天后**
- ♾️ **永不过期**
- 🎯 **自定义日期时间**

### 3. 批量续期

```bash
npm run cli keys
# 选择 "🔄 续期即将过期的 API Key"
```

功能：
- 查找7天内即将过期的 Keys
- 支持全部续期30天或90天
- 支持逐个选择续期

### 4. 删除 API Keys

```bash
npm run cli keys
# 选择 "🗑️ 删除 API Key"
```

## Web 界面功能

### 创建时设置过期

在创建 API Key 时，可以选择：
- 永不过期
- 1天、7天、30天、90天、180天、365天
- 自定义日期

### 查看过期状态

API Key 列表中显示：
- 🔴 已过期（红色）
- 🟡 即将过期（7天内，黄色）
- 🟢 正常（绿色）
- ♾️ 永不过期

### 手动续期

对于已过期的 API Keys：
1. 点击"续期"按钮
2. 选择新的过期时间
3. 确认更新

## 自动清理机制

系统每小时自动运行清理任务：
- 检查所有 API Keys 的过期时间
- 将过期的 Keys 标记为禁用（`isActive = false`）
- 不删除数据，保留历史记录
- 记录清理日志

## 测试工具

### 1. 快速测试脚本

```bash
node scripts/test-apikey-expiry.js
```

创建5个测试 Keys：
- 已过期（1天前）
- 1小时后过期
- 1天后过期
- 7天后过期
- 永不过期

### 2. 迁移脚本

为现有 API Keys 设置默认30天过期时间：

```bash
# 预览（不实际修改）
npm run migrate:apikey-expiry:dry

# 执行迁移
npm run migrate:apikey-expiry
```

## 使用场景

### 1. 临时访问

为临时用户或测试创建短期 Key：
```bash
# 创建1天有效期的测试 Key
# 在 Web 界面或 CLI 中选择"1天"
```

### 2. 定期更新

为安全考虑，定期更新 Keys：
```bash
# 每30天自动过期，需要续期
# 创建时选择"30天"
```

### 3. 长期合作

为可信任的长期用户：
```bash
# 选择"365天"或"永不过期"
```

### 4. 测试过期功能

快速测试过期验证：
```bash
# 1. 创建1小时后过期的 Key
npm run cli keys
# 选择修改过期时间 -> 选择测试 Key -> 1小时后

# 2. 等待或手动触发清理
# 3. 验证 API 调用被拒绝
```

## API 响应

过期的 API Key 调用时返回：
```json
{
  "error": "Unauthorized",
  "message": "Invalid or inactive API key"
}
```

## 最佳实践

1. **定期审查**：定期检查即将过期的 Keys
2. **提前通知**：在过期前通知用户续期
3. **分级管理**：根据用户级别设置不同过期策略
4. **测试验证**：新功能上线前充分测试过期机制
5. **备份恢复**：使用数据导出工具备份 Key 信息

## 注意事项

- 过期的 Keys 不会被删除，只是禁用
- 可以随时续期已过期的 Keys
- 修改过期时间立即生效
- 清理任务每小时运行一次