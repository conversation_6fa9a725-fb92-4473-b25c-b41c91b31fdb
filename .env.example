# 🚀 Claude Relay Service Configuration

# 🌐 服务器配置
PORT=3000
HOST=0.0.0.0
NODE_ENV=production

# 🔐 安全配置
JWT_SECRET=your-jwt-secret-here
ADMIN_SESSION_TIMEOUT=86400000
API_KEY_PREFIX=cr_
ENCRYPTION_KEY=your-encryption-key-here

# 👤 管理员凭据（可选，不设置则自动生成）
# ADMIN_USERNAME=cr_admin_custom
# ADMIN_PASSWORD=your-secure-password

# 📊 Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_ENABLE_TLS=

# 🎯 Claude API 配置
CLAUDE_API_URL=https://api.anthropic.com/v1/messages
CLAUDE_API_VERSION=2023-06-01
CLAUDE_BETA_HEADER=claude-code-20250219,oauth-2025-04-20,interleaved-thinking-2025-05-14,fine-grained-tool-streaming-2025-05-14

# 🌐 代理配置
DEFAULT_PROXY_TIMEOUT=60000
MAX_PROXY_RETRIES=3

# 📈 使用限制
DEFAULT_TOKEN_LIMIT=1000000

# 📝 日志配置
LOG_LEVEL=info
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 🔧 系统配置
CLEANUP_INTERVAL=3600000
TOKEN_USAGE_RETENTION=**********
HEALTH_CHECK_INTERVAL=60000
SYSTEM_TIMEZONE=Asia/Shanghai
TIMEZONE_OFFSET=8

# 🎨 Web 界面配置
WEB_TITLE=Claude Relay Service
WEB_DESCRIPTION=Multi-account Claude API relay service with beautiful management interface
WEB_LOGO_URL=/assets/logo.png

# 🛠️ 开发配置
DEBUG=false
ENABLE_CORS=true
TRUST_PROXY=true

# 🔒 客户端限制（可选）
# ALLOW_CUSTOM_CLIENTS=false