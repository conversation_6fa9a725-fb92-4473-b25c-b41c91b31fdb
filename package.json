{"name": "claude-relay-service", "version": "1.0.0", "description": "Claude Code API relay service with multi-account management, OpenAI compatibility, and API key authentication", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "build:web": "cd web && npm run build", "install:web": "cd web && npm install", "setup": "node scripts/setup.js", "cli": "node cli/index.js", "service": "node scripts/manage.js", "service:start": "node scripts/manage.js start", "service:start:daemon": "node scripts/manage.js start -d", "service:start:d": "node scripts/manage.js start -d", "service:daemon": "node scripts/manage.js start -d", "service:stop": "node scripts/manage.js stop", "service:restart": "node scripts/manage.js restart", "service:restart:daemon": "node scripts/manage.js restart -d", "service:logs:follow": "node scripts/manage.js logs -f", "service:restart:d": "node scripts/manage.js restart -d", "service:status": "node scripts/manage.js status", "service:logs": "node scripts/manage.js logs", "test": "jest", "lint": "eslint src/**/*.js", "docker:build": "docker build -t claude-relay-service .", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "migrate:apikey-expiry": "node scripts/migrate-apikey-expiry.js", "migrate:apikey-expiry:dry": "node scripts/migrate-apikey-expiry.js --dry-run", "migrate:fix-usage-stats": "node scripts/fix-usage-stats.js", "data:export": "node scripts/data-transfer.js export", "data:import": "node scripts/data-transfer.js import", "data:export:sanitized": "node scripts/data-transfer.js export --sanitize", "data:export:enhanced": "node scripts/data-transfer-enhanced.js export", "data:export:encrypted": "node scripts/data-transfer-enhanced.js export --decrypt=false", "data:import:enhanced": "node scripts/data-transfer-enhanced.js import", "data:debug": "node scripts/debug-redis-keys.js"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^2.4.3", "chalk": "^4.1.2", "commander": "^11.1.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "google-auth-library": "^10.1.0", "helmet": "^7.1.0", "https-proxy-agent": "^7.0.2", "inquirer": "^8.2.6", "ioredis": "^5.3.2", "morgan": "^1.10.0", "ora": "^5.4.1", "rate-limiter-flexible": "^5.0.5", "socks-proxy-agent": "^8.0.2", "table": "^6.8.1", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@types/node": "^20.8.9", "eslint": "^8.53.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["claude", "api", "proxy", "relay", "claude-code", "anthropic"], "author": "Claude <PERSON>", "license": "MIT"}